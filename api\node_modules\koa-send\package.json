{"_args": [["koa-send@5.0.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "koa-send@5.0.1", "_id": "koa-send@5.0.1", "_inBundle": false, "_integrity": "sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==", "_location": "/koa-send", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "koa-send@5.0.1", "name": "koa-send", "escapedName": "koa-send", "rawSpec": "5.0.1", "saveSpec": null, "fetchSpec": "5.0.1"}, "_requiredBy": ["/koa-static"], "_resolved": "https://registry.npmmirror.com/koa-send/-/koa-send-5.0.1.tgz", "_spec": "5.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/koajs/send/issues"}, "dependencies": {"debug": "^4.1.1", "http-errors": "^1.7.3", "resolve-path": "^1.4.0"}, "description": "Transfer static files", "devDependencies": {"eslint": "^4.19.1", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "iltorb": "^2.3.2", "koa": "^2.5.1", "mocha": "^5.2.0", "nyc": "^15.0.0", "should": "^13.2.1", "supertest": "^3.1.0"}, "engines": {"node": ">= 8"}, "files": ["index.js"], "homepage": "https://github.com/koajs/send", "keywords": ["koa", "file", "static", "sendfile"], "license": "MIT", "main": "index.js", "name": "koa-send", "nyc": {"reporter": ["lcov", "text-summary"], "report-dir": "./coverage"}, "repository": {"type": "git", "url": "git://github.com/koajs/send.git"}, "scripts": {"lint": "eslint --fix .", "test": "npm run lint && mocha --require should --reporter spec --exit", "test-cov": "nyc npm run test"}, "version": "5.0.1"}