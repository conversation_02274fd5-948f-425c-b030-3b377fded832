{"_args": [["cache-content-type@1.0.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "cache-content-type@1.0.1", "_id": "cache-content-type@1.0.1", "_inBundle": false, "_integrity": "sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==", "_location": "/cache-content-type", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cache-content-type@1.0.1", "name": "cache-content-type", "escapedName": "cache-content-type", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/cache-content-type/-/cache-content-type-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "dead_horse"}, "bugs": {"url": "https://github.com/node-modules/cache-content-type/issues"}, "ci": {"version": "6, 8, 10"}, "dependencies": {"mime-types": "^2.1.18", "ylru": "^1.2.0"}, "description": "Create a full Content-Type header given a MIME type or extension and catch the result", "devDependencies": {"egg-bin": "^4.7.1", "egg-ci": "^1.8.0", "eslint": "^5.1.0", "eslint-config-egg": "^7.0.0", "mm": "^2.2.0"}, "engines": {"node": ">= 6.0.0"}, "files": ["index.js"], "homepage": "https://github.com/node-modules/cache-content-type#readme", "keywords": ["mime", "content-type", "lru"], "license": "MIT", "main": "index.js", "name": "cache-content-type", "repository": {"type": "git", "url": "git+https://github.com/node-modules/cache-content-type.git"}, "scripts": {"ci": "eslint . && npm run cov", "cov": "egg-bin cov", "test": "egg-bin test"}, "version": "1.0.1"}