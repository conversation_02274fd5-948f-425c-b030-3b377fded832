{"_args": [["depd@1.1.2", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "depd@1.1.2", "_id": "depd@1.1.2", "_inBundle": false, "_integrity": "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==", "_location": "/http-errors/depd", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "depd@1.1.2", "name": "depd", "escapedName": "depd", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/http-errors"], "_resolved": "https://registry.npmmirror.com/depd/-/depd-1.1.2.tgz", "_spec": "1.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "lib/browser/index.js", "bugs": {"url": "https://github.com/dougwilson/nodejs-depd/issues"}, "description": "Deprecate all the things", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "3.19.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "homepage": "https://github.com/dougwilson/nodejs-depd#readme", "keywords": ["deprecate", "deprecated"], "license": "MIT", "name": "depd", "repository": {"type": "git", "url": "git+https://github.com/dougwilson/nodejs-depd.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --no-exit test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/"}, "version": "1.1.2"}