{"_args": [["koa-bodyparser@4.4.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "koa-bodyparser@4.4.1", "_id": "koa-bodyparser@4.4.1", "_inBundle": false, "_integrity": "sha512-kBH3IYPMb+iAXnrxIhXnW+gXV8OTzCu8VPDqvcDHW9SQrbkHmqPQtiZwrltNmSq6/lpipHnT7k7PsjlVD7kK0w==", "_location": "/koa-bodyparser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "koa-bodyparser@4.4.1", "name": "koa-bodyparser", "escapedName": "koa-bodyparser", "rawSpec": "4.4.1", "saveSpec": null, "fetchSpec": "4.4.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/koa-bodyparser/-/koa-bodyparser-4.4.1.tgz", "_spec": "4.4.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "dead_horse", "email": "<EMAIL>", "url": " http://deadhorse.me"}, "bugs": {"url": "https://github.com/koajs/body-parser/issues"}, "dependencies": {"co-body": "^6.0.0", "copy-to": "^2.0.1", "type-is": "^1.6.18"}, "description": "a body parser for <PERSON><PERSON>", "devDependencies": {"eslint-config-xo-lass": "^1.0.3", "husky": "^4.2.5", "koa": "^2", "mocha": "^10.2.0", "nyc": "^15.0.1", "should": "^13.2.3", "supertest": "^4.0.2", "xo": "0.25.4"}, "engines": {"node": ">=8.0.0"}, "files": ["index.js"], "homepage": "https://github.com/koajs/body-parser", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "json", "u<PERSON><PERSON><PERSON>", "koa", "body"], "license": "MIT", "main": "index.js", "name": "koa-bodyparser", "repository": {"type": "git", "url": "git://github.com/koajs/bodyparser.git"}, "scripts": {"ci": "npm run lint && npm run coverage", "coverage": "nyc npm run test --reporter=lcov", "lint": "xo", "lint:fix": "xo --fix", "test": "mocha --require should test/*.spec.js --exit"}, "version": "4.4.1", "xo": {"prettier": true, "space": true, "extends": ["xo-lass"], "rules": {"node/no-deprecated-api": "off", "no-unused-vars": "off", "no-prototype-builtins": "off", "prefer-rest-params": "off"}, "ignores": ["test/**"]}}