{"_args": [["cookies@0.8.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "cookies@0.8.0", "_id": "cookies@0.8.0", "_inBundle": false, "_integrity": "sha512-8aPsApQfebXnuI+537McwYsDtjVxGm8gTIzQI3FDW6t5t/DAhERxtnbEPN/8RX+uZthoz4eCOgloXaE5cYyNow==", "_location": "/cookies", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cookies@0.8.0", "name": "cookies", "escapedName": "cookies", "rawSpec": "0.8.0", "saveSpec": null, "fetchSpec": "0.8.0"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/cookies/-/cookies-0.8.0.tgz", "_spec": "0.8.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jed.is"}, "bugs": {"url": "https://github.com/pillarjs/cookies/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"depd": "~2.0.0", "keygrip": "~1.1.0"}, "description": "Cookies, optionally signed using Keygrip.", "devDependencies": {"eslint": "4.19.1", "express": "4.17.1", "mocha": "6.2.1", "nyc": "14.1.1", "restify": "8.4.0", "supertest": "4.0.2"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/pillarjs/cookies#readme", "license": "MIT", "name": "cookies", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/cookies.git"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "0.8.0"}