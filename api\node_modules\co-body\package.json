{"_args": [["co-body@6.1.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "co-body@6.1.0", "_id": "co-body@6.1.0", "_inBundle": false, "_integrity": "sha512-m7pOT6CdLN7FuXUcpuz/8lfQ/L77x8SchHCF4G0RBTJO20Wzmhn5Sp4/5WsKy8OSpifBSUrmg83qEqaDHdyFuQ==", "_location": "/co-body", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "co-body@6.1.0", "name": "co-body", "escapedName": "co-body", "rawSpec": "6.1.0", "saveSpec": null, "fetchSpec": "6.1.0"}, "_requiredBy": ["/koa-bodyparser"], "_resolved": "https://registry.npmmirror.com/co-body/-/co-body-6.1.0.tgz", "_spec": "6.1.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/cojs/co-body/issues"}, "dependencies": {"inflation": "^2.0.0", "qs": "^6.5.2", "raw-body": "^2.3.3", "type-is": "^1.6.16"}, "description": "request body parsing for co", "devDependencies": {"autod": "^3.0.1", "egg-bin": "^4.7.0", "eslint": "^4.19.1", "eslint-config-egg": "^7.0.0", "koa": "^1.6.0", "safe-qs": "^6.0.1", "should": "^11.2.0", "supertest": "^3.1.0"}, "files": ["index.js", "lib/"], "homepage": "https://github.com/cojs/co-body#readme", "keywords": ["request", "parse", "parser", "json", "co", "generators", "u<PERSON><PERSON><PERSON>"], "license": "MIT", "name": "co-body", "repository": {"type": "git", "url": "git+https://github.com/cojs/co-body.git"}, "scripts": {"autod": "autod", "cov": "eslint . && egg-bin cov -r should", "lint": "eslint .", "test": "egg-bin test -r should"}, "version": "6.1.0"}