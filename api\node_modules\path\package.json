{"_args": [["path@0.12.7", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "path@0.12.7", "_id": "path@0.12.7", "_inBundle": false, "_integrity": "sha512-aXXC6s+1w7otVF9UletFkFcDsJeO7lSZBPUQhtb5O0xJe8LtYhj/GxldoL09bBj9+ZmE2hNoHqQSFMN5fikh4Q==", "_location": "/path", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "path@0.12.7", "name": "path", "escapedName": "path", "rawSpec": "0.12.7", "saveSpec": null, "fetchSpec": "0.12.7"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/path/-/path-0.12.7.tgz", "_spec": "0.12.7", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "bugs": {"url": "https://github.com/jinder/path/issues"}, "dependencies": {"process": "^0.11.1", "util": "^0.10.3"}, "description": "Node.JS path module", "homepage": "http://nodejs.org/docs/latest/api/path.html", "keywords": ["ender", "path"], "license": "MIT", "main": "./path.js", "name": "path", "repository": {"type": "git", "url": "git://github.com/jinder/path.git"}, "version": "0.12.7"}