{"_args": [["has@1.0.3", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "has@1.0.3", "_id": "has@1.0.3", "_inBundle": false, "_integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "_location": "/has", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has@1.0.3", "name": "has", "escapedName": "has", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/get-intrinsic"], "_resolved": "https://registry.npmmirror.com/has/-/has-1.0.3.tgz", "_spec": "1.0.3", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tarruda/has/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"function-bind": "^1.1.1"}, "description": "Object.prototype.hasOwnProperty.call shortcut", "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "eslint": "^4.19.1", "tape": "^4.9.0"}, "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/tarruda/has", "license": "MIT", "licenses": [{"type": "MIT", "url": "https://github.com/tarruda/has/blob/master/LICENSE-MIT"}], "main": "./src", "name": "has", "repository": {"type": "git", "url": "git://github.com/tarruda/has.git"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "tape test"}, "version": "1.0.3"}