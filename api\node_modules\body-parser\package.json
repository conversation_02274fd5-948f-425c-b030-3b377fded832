{"_from": "body-parser@1.20.1", "_id": "body-parser@1.20.1", "_inBundle": false, "_integrity": "sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==", "_location": "/body-parser", "_phantomChildren": {"bytes": "3.1.2", "depd": "2.0.0", "iconv-lite": "0.4.24", "inherits": "2.0.4", "setprototypeof": "1.2.0", "side-channel": "1.0.4", "toidentifier": "1.0.1", "unpipe": "1.0.0"}, "_requested": {"type": "version", "registry": true, "raw": "body-parser@1.20.1", "name": "body-parser", "escapedName": "body-parser", "rawSpec": "1.20.1", "saveSpec": null, "fetchSpec": "1.20.1"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.1.tgz", "_shasum": "b1812a8912c195cd371a3ee5e66faa2338a5c668", "_spec": "body-parser@1.20.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口\\node_modules\\express", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "deprecated": false, "description": "Node.js body parsing middleware", "devDependencies": {"eslint": "8.24.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.0.1", "eslint-plugin-standard": "4.1.0", "methods": "1.1.2", "mocha": "10.0.0", "nyc": "15.1.0", "safe-buffer": "5.2.1", "supertest": "6.3.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}, "files": ["lib/", "LICENSE", "HISTORY.md", "SECURITY.md", "index.js"], "homepage": "https://github.com/expressjs/body-parser#readme", "license": "MIT", "name": "body-parser", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.20.1"}