{"_args": [["process@0.11.10", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "process@0.11.10", "_id": "process@0.11.10", "_inBundle": false, "_integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==", "_location": "/process", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "process@0.11.10", "name": "process", "escapedName": "process", "rawSpec": "0.11.10", "saveSpec": null, "fetchSpec": "0.11.10"}, "_requiredBy": ["/path"], "_resolved": "https://registry.npmmirror.com/process/-/process-0.11.10.tgz", "_spec": "0.11.10", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "./browser.js", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "description": "process information for node.js and browsers", "devDependencies": {"mocha": "2.2.1", "zuul": "^3.10.3"}, "engines": {"node": ">= 0.6.0"}, "homepage": "https://github.com/shtylman/node-process#readme", "keywords": ["process"], "license": "MIT", "main": "./index.js", "name": "process", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "scripts": {"browser": "zuul --no-coverage --ui mocha-bdd --local 8080 -- test.js", "test": "mocha test.js"}, "version": "0.11.10"}