{"_args": [["object-inspect@1.12.3", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "object-inspect@1.12.3", "_id": "object-inspect@1.12.3", "_inBundle": false, "_integrity": "sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==", "_location": "/object-inspect", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "object-inspect@1.12.3", "name": "object-inspect", "escapedName": "object-inspect", "rawSpec": "1.12.3", "saveSpec": null, "fetchSpec": "1.12.3"}, "_requiredBy": ["/side-channel"], "_resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.3.tgz", "_spec": "1.12.3", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"./util.inspect.js": false}, "bugs": {"url": "https://github.com/inspect-js/object-inspect/issues"}, "description": "string representations of objects in node and the browser", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "@pkgjs/support": "^0.0.6", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "error-cause": "^1.0.5", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-tostringtag": "^1.0.0", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "mock-property": "^1.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "string.prototype.repeat": "^1.0.0", "tape": "^5.6.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "license": "MIT", "main": "index.js", "name": "object-inspect", "publishConfig": {"ignore": [".github/workflows", "./test-core-js.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/object-inspect.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "npx @pkgjs/support validate", "posttest": "npx aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:corejs", "test:corejs": "nyc tape test-core-js.js 'test/*.js'", "tests-only": "nyc tape 'test/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "support": true, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "version": "1.12.3"}