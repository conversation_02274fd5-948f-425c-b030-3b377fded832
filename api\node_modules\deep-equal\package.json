{"_args": [["deep-equal@1.0.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "deep-equal@1.0.1", "_id": "deep-equal@1.0.1", "_inBundle": false, "_integrity": "sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==", "_location": "/deep-equal", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "deep-equal@1.0.1", "name": "deep-equal", "escapedName": "deep-equal", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/http-assert"], "_resolved": "https://registry.npmmirror.com/deep-equal/-/deep-equal-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "description": "node's assert.deepEqual algorithm", "devDependencies": {"tape": "^3.5.0"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "homepage": "https://github.com/substack/node-deep-equal#readme", "keywords": ["equality", "equal", "compare"], "license": "MIT", "main": "index.js", "name": "deep-equal", "repository": {"type": "git", "url": "git+ssh://**************/substack/node-deep-equal.git"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "version": "1.0.1"}