{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAiBA;;GAEG;AACH,SAAS,KAAK,CAAC,GAAW;IACxB,IAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;QACrB,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAEpB,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;YAChD,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC7D,SAAS;SACV;QAED,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACnE,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACzD,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC1D,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEd,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;gBACrB,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAE/B;gBACE,QAAQ;gBACR,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;oBAC1B,QAAQ;oBACR,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;oBAC1B,QAAQ;oBACR,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC;oBAC3B,MAAM;oBACN,IAAI,KAAK,EAAE,EACX;oBACA,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;oBACjB,SAAS;iBACV;gBAED,MAAM;aACP;YAED,IAAI,CAAC,IAAI;gBAAE,MAAM,IAAI,SAAS,CAAC,oCAA6B,CAAC,CAAE,CAAC,CAAC;YAEjE,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACrD,CAAC,GAAG,CAAC,CAAC;YACN,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEd,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAClB,MAAM,IAAI,SAAS,CAAC,6CAAoC,CAAC,CAAE,CAAC,CAAC;aAC9D;YAED,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;gBACrB,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACnB,OAAO,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;oBAC/B,SAAS;iBACV;gBAED,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAClB,KAAK,EAAE,CAAC;oBACR,IAAI,KAAK,KAAK,CAAC,EAAE;wBACf,CAAC,EAAE,CAAC;wBACJ,MAAM;qBACP;iBACF;qBAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBACzB,KAAK,EAAE,CAAC;oBACR,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;wBACtB,MAAM,IAAI,SAAS,CAAC,8CAAuC,CAAC,CAAE,CAAC,CAAC;qBACjE;iBACF;gBAED,OAAO,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;aACrB;YAED,IAAI,KAAK;gBAAE,MAAM,IAAI,SAAS,CAAC,gCAAyB,CAAC,CAAE,CAAC,CAAC;YAC7D,IAAI,CAAC,OAAO;gBAAE,MAAM,IAAI,SAAS,CAAC,6BAAsB,CAAC,CAAE,CAAC,CAAC;YAE7D,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;YAC3D,CAAC,GAAG,CAAC,CAAC;YACN,SAAS;SACV;QAED,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KAC1D;IAED,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;IAElD,OAAO,MAAM,CAAC;AAChB,CAAC;AAaD;;GAEG;AACH,SAAgB,KAAK,CAAC,GAAW,EAAE,OAA0B;IAA1B,wBAAA,EAAA,YAA0B;IAC3D,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,IAAA,KAAoB,OAAO,SAAZ,EAAf,QAAQ,mBAAG,IAAI,KAAA,CAAa;IACpC,IAAM,cAAc,GAAG,YAAK,YAAY,CAAC,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,QAAK,CAAC;IAC1E,IAAM,MAAM,GAAY,EAAE,CAAC;IAC3B,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,IAAI,GAAG,EAAE,CAAC;IAEd,IAAM,UAAU,GAAG,UAAC,IAAsB;QACxC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI;YAAE,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;IAC7E,CAAC,CAAC;IAEF,IAAM,WAAW,GAAG,UAAC,IAAsB;QACzC,IAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAChC,IAAA,KAA4B,MAAM,CAAC,CAAC,CAAC,EAA7B,QAAQ,UAAA,EAAE,KAAK,WAAc,CAAC;QAC5C,MAAM,IAAI,SAAS,CAAC,qBAAc,QAAQ,iBAAO,KAAK,wBAAc,IAAI,CAAE,CAAC,CAAC;IAC9E,CAAC,CAAC;IAEF,IAAM,WAAW,GAAG;QAClB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,KAAyB,CAAC;QAC9B,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,CAAC,EAAE;YACjE,MAAM,IAAI,KAAK,CAAC;SACjB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;QACxB,IAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAChC,IAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAChC,IAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAI,IAAI,IAAI,OAAO,EAAE;YACnB,IAAI,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YAExB,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnC,IAAI,IAAI,MAAM,CAAC;gBACf,MAAM,GAAG,EAAE,CAAC;aACb;YAED,IAAI,IAAI,EAAE;gBACR,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClB,IAAI,GAAG,EAAE,CAAC;aACX;YAED,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,IAAI,IAAI,GAAG,EAAE;gBACnB,MAAM,QAAA;gBACN,MAAM,EAAE,EAAE;gBACV,OAAO,EAAE,OAAO,IAAI,cAAc;gBAClC,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE;aACvC,CAAC,CAAC;YACH,SAAS;SACV;QAED,IAAM,KAAK,GAAG,IAAI,IAAI,UAAU,CAAC,cAAc,CAAC,CAAC;QACjD,IAAI,KAAK,EAAE;YACT,IAAI,IAAI,KAAK,CAAC;YACd,SAAS;SACV;QAED,IAAI,IAAI,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,IAAI,GAAG,EAAE,CAAC;SACX;QAED,IAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,IAAI,EAAE;YACR,IAAM,MAAM,GAAG,WAAW,EAAE,CAAC;YAC7B,IAAM,MAAI,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACtC,IAAM,SAAO,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YAC5C,IAAM,MAAM,GAAG,WAAW,EAAE,CAAC;YAE7B,WAAW,CAAC,OAAO,CAAC,CAAC;YAErB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAI,IAAI,CAAC,SAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpC,OAAO,EAAE,MAAI,IAAI,CAAC,SAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAO;gBACpD,MAAM,QAAA;gBACN,MAAM,QAAA;gBACN,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE;aACvC,CAAC,CAAC;YACH,SAAS;SACV;QAED,WAAW,CAAC,KAAK,CAAC,CAAC;KACpB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AA3FD,sBA2FC;AAiBD;;GAEG;AACH,SAAgB,OAAO,CACrB,GAAW,EACX,OAAgD;IAEhD,OAAO,gBAAgB,CAAI,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC;AALD,0BAKC;AAID;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,MAAe,EACf,OAAqC;IAArC,wBAAA,EAAA,YAAqC;IAErC,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IACvB,IAAA,KAA+C,OAAO,OAA7B,EAAzB,MAAM,mBAAG,UAAC,CAAS,IAAK,OAAA,CAAC,EAAD,CAAC,KAAA,EAAE,KAAoB,OAAO,SAAZ,EAAf,QAAQ,mBAAG,IAAI,KAAA,CAAa;IAE/D,uCAAuC;IACvC,IAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK;QAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,IAAI,MAAM,CAAC,cAAO,KAAK,CAAC,OAAO,OAAI,EAAE,OAAO,CAAC,CAAC;SACtD;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,UAAC,IAA4C;QAClD,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAExB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAI,IAAI,KAAK,CAAC;gBACd,SAAS;aACV;YAED,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAClD,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC;YAClE,IAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC;YAEhE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,SAAS,CACjB,qBAAa,KAAK,CAAC,IAAI,uCAAmC,CAC3D,CAAC;iBACH;gBAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACtB,IAAI,QAAQ;wBAAE,SAAS;oBAEvB,MAAM,IAAI,SAAS,CAAC,qBAAa,KAAK,CAAC,IAAI,uBAAmB,CAAC,CAAC;iBACjE;gBAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACrC,IAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAExC,IAAI,QAAQ,IAAI,CAAE,OAAO,CAAC,CAAC,CAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;wBACrD,MAAM,IAAI,SAAS,CACjB,yBAAiB,KAAK,CAAC,IAAI,2BAAe,KAAK,CAAC,OAAO,2BAAe,OAAO,OAAG,CACjF,CAAC;qBACH;oBAED,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;iBAC/C;gBAED,SAAS;aACV;YAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC1D,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;gBAE7C,IAAI,QAAQ,IAAI,CAAE,OAAO,CAAC,CAAC,CAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;oBACrD,MAAM,IAAI,SAAS,CACjB,qBAAa,KAAK,CAAC,IAAI,2BAAe,KAAK,CAAC,OAAO,2BAAe,OAAO,OAAG,CAC7E,CAAC;iBACH;gBAED,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC9C,SAAS;aACV;YAED,IAAI,QAAQ;gBAAE,SAAS;YAEvB,IAAM,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;YACvD,MAAM,IAAI,SAAS,CAAC,qBAAa,KAAK,CAAC,IAAI,sBAAW,aAAa,CAAE,CAAC,CAAC;SACxE;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AA9ED,4CA8EC;AA8BD;;GAEG;AACH,SAAgB,KAAK,CACnB,GAAS,EACT,OAAwE;IAExE,IAAM,IAAI,GAAU,EAAE,CAAC;IACvB,IAAM,EAAE,GAAG,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5C,OAAO,gBAAgB,CAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAPD,sBAOC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,EAAU,EACV,IAAW,EACX,OAAqC;IAArC,wBAAA,EAAA,YAAqC;IAE7B,IAAA,KAA8B,OAAO,OAAZ,EAAzB,MAAM,mBAAG,UAAC,CAAS,IAAK,OAAA,CAAC,EAAD,CAAC,KAAA,CAAa;IAE9C,OAAO,UAAU,QAAgB;QAC/B,IAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAEb,IAAG,IAAI,GAAY,CAAC,GAAb,EAAE,KAAK,GAAK,CAAC,MAAN,CAAO;QAC7B,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gCAE1B,CAAC;YACR,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS;kCAAW;YAEjC,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAExB,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,EAAE;gBAChD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK;oBAC/D,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;aACtC;;QAXH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;oBAAxB,CAAC;SAYT;QAED,OAAO,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC;IACjC,CAAC,CAAC;AACJ,CAAC;AA9BD,4CA8BC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,GAAG,CAAC,OAAO,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;AAC1D,CAAC;AAED;;GAEG;AACH,SAAS,KAAK,CAAC,OAAiC;IAC9C,OAAO,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;AACjD,CAAC;AAkBD;;GAEG;AACH,SAAS,cAAc,CAAC,IAAY,EAAE,IAAY;IAChD,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,IAAM,WAAW,GAAG,yBAAyB,CAAC;IAE9C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/C,OAAO,UAAU,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC;YACR,kEAAkE;YAClE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;YAC9B,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;SACZ,CAAC,CAAC;QACH,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC5C;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CACpB,KAA6B,EAC7B,IAAY,EACZ,OAA8C;IAE9C,IAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,MAAM,EAAxC,CAAwC,CAAC,CAAC;IAC5E,OAAO,IAAI,MAAM,CAAC,aAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CACrB,IAAY,EACZ,IAAY,EACZ,OAA8C;IAE9C,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7D,CAAC;AAiCD;;GAEG;AACH,SAAgB,cAAc,CAC5B,MAAe,EACf,IAAY,EACZ,OAAmC;IAAnC,wBAAA,EAAA,YAAmC;IAGjC,IAAA,KAME,OAAO,OANK,EAAd,MAAM,mBAAG,KAAK,KAAA,EACd,KAKE,OAAO,MALG,EAAZ,KAAK,mBAAG,IAAI,KAAA,EACZ,KAIE,OAAO,IAJC,EAAV,GAAG,mBAAG,IAAI,KAAA,EACV,KAGE,OAAO,OAHgB,EAAzB,MAAM,mBAAG,UAAC,CAAS,IAAK,OAAA,CAAC,EAAD,CAAC,KAAA,EACzB,KAEE,OAAO,UAFQ,EAAjB,SAAS,mBAAG,KAAK,KAAA,EACjB,KACE,OAAO,SADI,EAAb,QAAQ,mBAAG,EAAE,KAAA,CACH;IACZ,IAAM,UAAU,GAAG,WAAI,YAAY,CAAC,QAAQ,CAAC,QAAK,CAAC;IACnD,IAAM,WAAW,GAAG,WAAI,YAAY,CAAC,SAAS,CAAC,MAAG,CAAC;IACnD,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAE7B,wDAAwD;IACxD,KAAoB,UAAM,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE;QAAvB,IAAM,KAAK,eAAA;QACd,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,KAAK,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAClD,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAElD,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,IAAI,IAAI;oBAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE3B,IAAI,MAAM,IAAI,MAAM,EAAE;oBACpB,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,EAAE;wBACpD,IAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC9C,KAAK,IAAI,aAAM,MAAM,iBAAO,KAAK,CAAC,OAAO,iBAAO,MAAM,SAAG,MAAM,gBAAM,KAAK,CAAC,OAAO,iBAAO,MAAM,cAAI,GAAG,CAAE,CAAC;qBAC1G;yBAAM;wBACL,KAAK,IAAI,aAAM,MAAM,cAAI,KAAK,CAAC,OAAO,cAAI,MAAM,cAAI,KAAK,CAAC,QAAQ,CAAE,CAAC;qBACtE;iBACF;qBAAM;oBACL,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,EAAE;wBACpD,KAAK,IAAI,cAAO,KAAK,CAAC,OAAO,cAAI,KAAK,CAAC,QAAQ,MAAG,CAAC;qBACpD;yBAAM;wBACL,KAAK,IAAI,WAAI,KAAK,CAAC,OAAO,cAAI,KAAK,CAAC,QAAQ,CAAE,CAAC;qBAChD;iBACF;aACF;iBAAM;gBACL,KAAK,IAAI,aAAM,MAAM,SAAG,MAAM,cAAI,KAAK,CAAC,QAAQ,CAAE,CAAC;aACpD;SACF;KACF;IAED,IAAI,GAAG,EAAE;QACP,IAAI,CAAC,MAAM;YAAE,KAAK,IAAI,UAAG,WAAW,MAAG,CAAC;QAExC,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAM,UAAU,MAAG,CAAC;KACxD;SAAM;QACL,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3C,IAAM,cAAc,GAClB,OAAO,QAAQ,KAAK,QAAQ;YAC1B,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACzD,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC;QAE7B,IAAI,CAAC,MAAM,EAAE;YACX,KAAK,IAAI,aAAM,WAAW,gBAAM,UAAU,QAAK,CAAC;SACjD;QAED,IAAI,CAAC,cAAc,EAAE;YACnB,KAAK,IAAI,aAAM,WAAW,cAAI,UAAU,MAAG,CAAC;SAC7C;KACF;IAED,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3C,CAAC;AArED,wCAqEC;AAOD;;;;;;GAMG;AACH,SAAgB,YAAY,CAC1B,IAAU,EACV,IAAY,EACZ,OAA8C;IAE9C,IAAI,IAAI,YAAY,MAAM;QAAE,OAAO,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9D,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAAE,OAAO,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACnE,OAAO,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,CAAC;AARD,oCAQC", "sourcesContent": ["/**\n * Tokenizer results.\n */\ninterface LexToken {\n  type:\n    | \"OPEN\"\n    | \"CLOSE\"\n    | \"PATTERN\"\n    | \"NAME\"\n    | \"CHAR\"\n    | \"ESCAPED_CHAR\"\n    | \"MODIFIER\"\n    | \"END\";\n  index: number;\n  value: string;\n}\n\n/**\n * Tokenize input string.\n */\nfunction lexer(str: string): LexToken[] {\n  const tokens: LexToken[] = [];\n  let i = 0;\n\n  while (i < str.length) {\n    const char = str[i];\n\n    if (char === \"*\" || char === \"+\" || char === \"?\") {\n      tokens.push({ type: \"MODIFIER\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"\\\\\") {\n      tokens.push({ type: \"ESCAPED_CHAR\", index: i++, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"{\") {\n      tokens.push({ type: \"OPEN\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"}\") {\n      tokens.push({ type: \"CLOSE\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \":\") {\n      let name = \"\";\n      let j = i + 1;\n\n      while (j < str.length) {\n        const code = str.charCodeAt(j);\n\n        if (\n          // `0-9`\n          (code >= 48 && code <= 57) ||\n          // `A-Z`\n          (code >= 65 && code <= 90) ||\n          // `a-z`\n          (code >= 97 && code <= 122) ||\n          // `_`\n          code === 95\n        ) {\n          name += str[j++];\n          continue;\n        }\n\n        break;\n      }\n\n      if (!name) throw new TypeError(`Missing parameter name at ${i}`);\n\n      tokens.push({ type: \"NAME\", index: i, value: name });\n      i = j;\n      continue;\n    }\n\n    if (char === \"(\") {\n      let count = 1;\n      let pattern = \"\";\n      let j = i + 1;\n\n      if (str[j] === \"?\") {\n        throw new TypeError(`Pattern cannot start with \"?\" at ${j}`);\n      }\n\n      while (j < str.length) {\n        if (str[j] === \"\\\\\") {\n          pattern += str[j++] + str[j++];\n          continue;\n        }\n\n        if (str[j] === \")\") {\n          count--;\n          if (count === 0) {\n            j++;\n            break;\n          }\n        } else if (str[j] === \"(\") {\n          count++;\n          if (str[j + 1] !== \"?\") {\n            throw new TypeError(`Capturing groups are not allowed at ${j}`);\n          }\n        }\n\n        pattern += str[j++];\n      }\n\n      if (count) throw new TypeError(`Unbalanced pattern at ${i}`);\n      if (!pattern) throw new TypeError(`Missing pattern at ${i}`);\n\n      tokens.push({ type: \"PATTERN\", index: i, value: pattern });\n      i = j;\n      continue;\n    }\n\n    tokens.push({ type: \"CHAR\", index: i, value: str[i++] });\n  }\n\n  tokens.push({ type: \"END\", index: i, value: \"\" });\n\n  return tokens;\n}\n\nexport interface ParseOptions {\n  /**\n   * Set the default delimiter for repeat parameters. (default: `'/'`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters to automatically consider prefixes when parsing.\n   */\n  prefixes?: string;\n}\n\n/**\n * Parse a string for the raw tokens.\n */\nexport function parse(str: string, options: ParseOptions = {}): Token[] {\n  const tokens = lexer(str);\n  const { prefixes = \"./\" } = options;\n  const defaultPattern = `[^${escapeString(options.delimiter || \"/#?\")}]+?`;\n  const result: Token[] = [];\n  let key = 0;\n  let i = 0;\n  let path = \"\";\n\n  const tryConsume = (type: LexToken[\"type\"]): string | undefined => {\n    if (i < tokens.length && tokens[i].type === type) return tokens[i++].value;\n  };\n\n  const mustConsume = (type: LexToken[\"type\"]): string => {\n    const value = tryConsume(type);\n    if (value !== undefined) return value;\n    const { type: nextType, index } = tokens[i];\n    throw new TypeError(`Unexpected ${nextType} at ${index}, expected ${type}`);\n  };\n\n  const consumeText = (): string => {\n    let result = \"\";\n    let value: string | undefined;\n    while ((value = tryConsume(\"CHAR\") || tryConsume(\"ESCAPED_CHAR\"))) {\n      result += value;\n    }\n    return result;\n  };\n\n  while (i < tokens.length) {\n    const char = tryConsume(\"CHAR\");\n    const name = tryConsume(\"NAME\");\n    const pattern = tryConsume(\"PATTERN\");\n\n    if (name || pattern) {\n      let prefix = char || \"\";\n\n      if (prefixes.indexOf(prefix) === -1) {\n        path += prefix;\n        prefix = \"\";\n      }\n\n      if (path) {\n        result.push(path);\n        path = \"\";\n      }\n\n      result.push({\n        name: name || key++,\n        prefix,\n        suffix: \"\",\n        pattern: pattern || defaultPattern,\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    const value = char || tryConsume(\"ESCAPED_CHAR\");\n    if (value) {\n      path += value;\n      continue;\n    }\n\n    if (path) {\n      result.push(path);\n      path = \"\";\n    }\n\n    const open = tryConsume(\"OPEN\");\n    if (open) {\n      const prefix = consumeText();\n      const name = tryConsume(\"NAME\") || \"\";\n      const pattern = tryConsume(\"PATTERN\") || \"\";\n      const suffix = consumeText();\n\n      mustConsume(\"CLOSE\");\n\n      result.push({\n        name: name || (pattern ? key++ : \"\"),\n        pattern: name && !pattern ? defaultPattern : pattern,\n        prefix,\n        suffix,\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    mustConsume(\"END\");\n  }\n\n  return result;\n}\n\nexport interface TokensToFunctionOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * Function for encoding input strings for output.\n   */\n  encode?: (value: string, token: Key) => string;\n  /**\n   * When `false` the function can produce an invalid (unmatched) path. (default: `true`)\n   */\n  validate?: boolean;\n}\n\n/**\n * Compile a string to a template function for the path.\n */\nexport function compile<P extends object = object>(\n  str: string,\n  options?: ParseOptions & TokensToFunctionOptions\n) {\n  return tokensToFunction<P>(parse(str, options), options);\n}\n\nexport type PathFunction<P extends object = object> = (data?: P) => string;\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nexport function tokensToFunction<P extends object = object>(\n  tokens: Token[],\n  options: TokensToFunctionOptions = {}\n): PathFunction<P> {\n  const reFlags = flags(options);\n  const { encode = (x: string) => x, validate = true } = options;\n\n  // Compile all the tokens into regexps.\n  const matches = tokens.map((token) => {\n    if (typeof token === \"object\") {\n      return new RegExp(`^(?:${token.pattern})$`, reFlags);\n    }\n  });\n\n  return (data: Record<string, any> | null | undefined) => {\n    let path = \"\";\n\n    for (let i = 0; i < tokens.length; i++) {\n      const token = tokens[i];\n\n      if (typeof token === \"string\") {\n        path += token;\n        continue;\n      }\n\n      const value = data ? data[token.name] : undefined;\n      const optional = token.modifier === \"?\" || token.modifier === \"*\";\n      const repeat = token.modifier === \"*\" || token.modifier === \"+\";\n\n      if (Array.isArray(value)) {\n        if (!repeat) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to not repeat, but got an array`\n          );\n        }\n\n        if (value.length === 0) {\n          if (optional) continue;\n\n          throw new TypeError(`Expected \"${token.name}\" to not be empty`);\n        }\n\n        for (let j = 0; j < value.length; j++) {\n          const segment = encode(value[j], token);\n\n          if (validate && !(matches[i] as RegExp).test(segment)) {\n            throw new TypeError(\n              `Expected all \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`\n            );\n          }\n\n          path += token.prefix + segment + token.suffix;\n        }\n\n        continue;\n      }\n\n      if (typeof value === \"string\" || typeof value === \"number\") {\n        const segment = encode(String(value), token);\n\n        if (validate && !(matches[i] as RegExp).test(segment)) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`\n          );\n        }\n\n        path += token.prefix + segment + token.suffix;\n        continue;\n      }\n\n      if (optional) continue;\n\n      const typeOfMessage = repeat ? \"an array\" : \"a string\";\n      throw new TypeError(`Expected \"${token.name}\" to be ${typeOfMessage}`);\n    }\n\n    return path;\n  };\n}\n\nexport interface RegexpToFunctionOptions {\n  /**\n   * Function for decoding strings for params.\n   */\n  decode?: (value: string, token: Key) => string;\n}\n\n/**\n * A match result contains data about the path match.\n */\nexport interface MatchResult<P extends object = object> {\n  path: string;\n  index: number;\n  params: P;\n}\n\n/**\n * A match is either `false` (no match) or a match result.\n */\nexport type Match<P extends object = object> = false | MatchResult<P>;\n\n/**\n * The match function takes a string and returns whether it matched the path.\n */\nexport type MatchFunction<P extends object = object> = (\n  path: string\n) => Match<P>;\n\n/**\n * Create path match function from `path-to-regexp` spec.\n */\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions\n) {\n  const keys: Key[] = [];\n  const re = pathToRegexp(str, keys, options);\n  return regexpToFunction<P>(re, keys, options);\n}\n\n/**\n * Create a path match function from `path-to-regexp` output.\n */\nexport function regexpToFunction<P extends object = object>(\n  re: RegExp,\n  keys: Key[],\n  options: RegexpToFunctionOptions = {}\n): MatchFunction<P> {\n  const { decode = (x: string) => x } = options;\n\n  return function (pathname: string) {\n    const m = re.exec(pathname);\n    if (!m) return false;\n\n    const { 0: path, index } = m;\n    const params = Object.create(null);\n\n    for (let i = 1; i < m.length; i++) {\n      if (m[i] === undefined) continue;\n\n      const key = keys[i - 1];\n\n      if (key.modifier === \"*\" || key.modifier === \"+\") {\n        params[key.name] = m[i].split(key.prefix + key.suffix).map((value) => {\n          return decode(value, key);\n        });\n      } else {\n        params[key.name] = decode(m[i], key);\n      }\n    }\n\n    return { path, index, params };\n  };\n}\n\n/**\n * Escape a regular expression string.\n */\nfunction escapeString(str: string) {\n  return str.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, \"\\\\$1\");\n}\n\n/**\n * Get the flags for a regexp from the options.\n */\nfunction flags(options?: { sensitive?: boolean }) {\n  return options && options.sensitive ? \"\" : \"i\";\n}\n\n/**\n * Metadata about a key.\n */\nexport interface Key {\n  name: string | number;\n  prefix: string;\n  suffix: string;\n  pattern: string;\n  modifier: string;\n}\n\n/**\n * A token is a string (nothing special) or key metadata (capture group).\n */\nexport type Token = string | Key;\n\n/**\n * Pull out keys from a regexp.\n */\nfunction regexpToRegexp(path: RegExp, keys?: Key[]): RegExp {\n  if (!keys) return path;\n\n  const groupsRegex = /\\((?:\\?<(.*?)>)?(?!\\?)/g;\n\n  let index = 0;\n  let execResult = groupsRegex.exec(path.source);\n  while (execResult) {\n    keys.push({\n      // Use parenthesized substring match if available, index otherwise\n      name: execResult[1] || index++,\n      prefix: \"\",\n      suffix: \"\",\n      modifier: \"\",\n      pattern: \"\",\n    });\n    execResult = groupsRegex.exec(path.source);\n  }\n\n  return path;\n}\n\n/**\n * Transform an array into a regexp.\n */\nfunction arrayToRegexp(\n  paths: Array<string | RegExp>,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n): RegExp {\n  const parts = paths.map((path) => pathToRegexp(path, keys, options).source);\n  return new RegExp(`(?:${parts.join(\"|\")})`, flags(options));\n}\n\n/**\n * Create a path regexp from string input.\n */\nfunction stringToRegexp(\n  path: string,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n) {\n  return tokensToRegexp(parse(path, options), keys, options);\n}\n\nexport interface TokensToRegexpOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * When `true` the regexp won't allow an optional trailing delimiter to match. (default: `false`)\n   */\n  strict?: boolean;\n  /**\n   * When `true` the regexp will match to the end of the string. (default: `true`)\n   */\n  end?: boolean;\n  /**\n   * When `true` the regexp will match from the beginning of the string. (default: `true`)\n   */\n  start?: boolean;\n  /**\n   * Sets the final character for non-ending optimistic matches. (default: `/`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters that can also be \"end\" characters.\n   */\n  endsWith?: string;\n  /**\n   * Encode path tokens for use in the `RegExp`.\n   */\n  encode?: (value: string) => string;\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n */\nexport function tokensToRegexp(\n  tokens: Token[],\n  keys?: Key[],\n  options: TokensToRegexpOptions = {}\n) {\n  const {\n    strict = false,\n    start = true,\n    end = true,\n    encode = (x: string) => x,\n    delimiter = \"/#?\",\n    endsWith = \"\",\n  } = options;\n  const endsWithRe = `[${escapeString(endsWith)}]|$`;\n  const delimiterRe = `[${escapeString(delimiter)}]`;\n  let route = start ? \"^\" : \"\";\n\n  // Iterate over the tokens and create our regexp string.\n  for (const token of tokens) {\n    if (typeof token === \"string\") {\n      route += escapeString(encode(token));\n    } else {\n      const prefix = escapeString(encode(token.prefix));\n      const suffix = escapeString(encode(token.suffix));\n\n      if (token.pattern) {\n        if (keys) keys.push(token);\n\n        if (prefix || suffix) {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            const mod = token.modifier === \"*\" ? \"?\" : \"\";\n            route += `(?:${prefix}((?:${token.pattern})(?:${suffix}${prefix}(?:${token.pattern}))*)${suffix})${mod}`;\n          } else {\n            route += `(?:${prefix}(${token.pattern})${suffix})${token.modifier}`;\n          }\n        } else {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            route += `((?:${token.pattern})${token.modifier})`;\n          } else {\n            route += `(${token.pattern})${token.modifier}`;\n          }\n        }\n      } else {\n        route += `(?:${prefix}${suffix})${token.modifier}`;\n      }\n    }\n  }\n\n  if (end) {\n    if (!strict) route += `${delimiterRe}?`;\n\n    route += !options.endsWith ? \"$\" : `(?=${endsWithRe})`;\n  } else {\n    const endToken = tokens[tokens.length - 1];\n    const isEndDelimited =\n      typeof endToken === \"string\"\n        ? delimiterRe.indexOf(endToken[endToken.length - 1]) > -1\n        : endToken === undefined;\n\n    if (!strict) {\n      route += `(?:${delimiterRe}(?=${endsWithRe}))?`;\n    }\n\n    if (!isEndDelimited) {\n      route += `(?=${delimiterRe}|${endsWithRe})`;\n    }\n  }\n\n  return new RegExp(route, flags(options));\n}\n\n/**\n * Supported `path-to-regexp` input types.\n */\nexport type Path = string | RegExp | Array<string | RegExp>;\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n */\nexport function pathToRegexp(\n  path: Path,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n) {\n  if (path instanceof RegExp) return regexpToRegexp(path, keys);\n  if (Array.isArray(path)) return arrayToRegexp(path, keys, options);\n  return stringToRegexp(path, keys, options);\n}\n"]}