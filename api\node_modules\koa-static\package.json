{"_args": [["koa-static@5.0.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "koa-static@5.0.0", "_id": "koa-static@5.0.0", "_inBundle": false, "_integrity": "sha512-UqyYyH5YEXaJrf9S8E23GoJFQZXkBVJ9zYYMPGz919MSX1KuvAcycIuS0ci150HCoPf4XQVhQ84Qf8xRPWxFaQ==", "_location": "/koa-static", "_phantomChildren": {"ms": "2.1.2"}, "_requested": {"type": "version", "registry": true, "raw": "koa-static@5.0.0", "name": "koa-static", "escapedName": "koa-static", "rawSpec": "5.0.0", "saveSpec": null, "fetchSpec": "5.0.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/koa-static/-/koa-static-5.0.0.tgz", "_spec": "5.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/koajs/static/issues"}, "dependencies": {"debug": "^3.1.0", "koa-send": "^5.0.0"}, "description": "Static file serving middleware for koa", "devDependencies": {"eslint": "^4.19.1", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "istanbul": "^0.4.5", "koa": "^2.5.1", "mocha": "^5.2.0", "supertest": "^3.1.0"}, "engines": {"node": ">= 7.6.0"}, "files": ["index.js"], "homepage": "https://github.com/koajs/static#readme", "keywords": ["koa", "middleware", "file", "static", "sendfile"], "license": "MIT", "name": "koa-static", "repository": {"type": "git", "url": "git+https://github.com/koajs/static.git"}, "scripts": {"lint": "eslint --fix .", "test": "mocha --harmony --reporter spec --exit", "test-cov": "node --harmony ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --exit", "test-travis": "node --harmony ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --exit"}, "version": "5.0.0"}