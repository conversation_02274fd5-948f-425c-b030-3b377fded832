{"_args": [["copy-to@2.0.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "copy-to@2.0.1", "_id": "copy-to@2.0.1", "_inBundle": false, "_integrity": "sha512-3DdaFaU/Zf1AnpLiFDeNCD4TOWe3Zl2RZaTzUvWiIk5ERzcCodOE20Vqq4fzCbNoHURFHT4/us/Lfq+S2zyY4w==", "_location": "/copy-to", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "copy-to@2.0.1", "name": "copy-to", "escapedName": "copy-to", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/koa-bodyparser"], "_resolved": "https://registry.npmmirror.com/copy-to/-/copy-to-2.0.1.tgz", "_spec": "2.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "dead_horse", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/node-modules/copy-to/issues"}, "description": "copy an object's properties to another object", "devDependencies": {"mocha": "*", "should": "*"}, "files": ["index.js"], "homepage": "https://github.com/node-modules/copy-to", "keywords": ["copy", "object", "properties", "setter", "getter"], "license": "MIT", "main": "index.js", "name": "copy-to", "repository": {"type": "git", "url": "git://github.com/node-modules/copy-to.git"}, "scripts": {"test": "make test"}, "version": "2.0.1"}