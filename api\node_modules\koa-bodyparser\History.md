
4.4.1 / 2023-06-22
==================

**fixes**
  * [[`5a551b1`](http://github.com/koajs/bodyparser/commit/5a551b1de6f5e2200b8a838207b56ea1198bdb96)] - fix: compatible extra semicolon on content-type header (#153) (fengmk2 <<<EMAIL>>>)

4.4.0 / 2023-03-15
==================

**features**
  * [[`a9a6476`](http://github.com/koajs/bodyparser/commit/a9a647641bb883746c9691e86b8f87739df4e374)] - feat: Support scim json format (#151) (ask me anything :) <<<EMAIL>>>)

**fixes**
  * [[`4d931c6`](http://github.com/koajs/bodyparser/commit/4d931c634e9b59a843152f56d68b3ef2e1719675)] - fix: revert html parser, use text directly (dead-horse <<<EMAIL>>>)

**others**
  * [[`c02ec0c`](http://github.com/koajs/bodyparser/commit/c02ec0c062f92e1114b4196534669367eae14ccc)] - Update README.md (#149) (sgywzy <<<EMAIL>>>)
  * [[`85b426f`](http://github.com/koajs/bodyparser/commit/85b426fea3d98481fd4acbafce0857189199426e)] - Recommend @koa/multer for multipart/form-data (#145) (Jim Fisher <<<EMAIL>>>)
  * [[`afecb1a`](http://github.com/koajs/bodyparser/commit/afecb1ab7303ebd36d1a50d6bfe5fc3125759e43)] - Update Repo + Add Html Parser (#134) (imed jaberi <<<EMAIL>>>)
  * [[`ecc6ebf`](http://github.com/koajs/bodyparser/commit/ecc6ebfad7179e0009501723e7b2227d25c9603d)] - docs: fix broken npmjs link (#132) (Joel Colucci <<<EMAIL>>>)
  * [[`336b287`](http://github.com/koajs/bodyparser/commit/336b2879dc7c0e048d79e28bf23d4b8fe2589376)] - Update README.md (haoxin <<<EMAIL>>>)
  * [[`e02cb7d`](http://github.com/koajs/bodyparser/commit/e02cb7dd2c798a116ef12c776da30c710697dea5)] - Update README.md (#125) (thaiworldgame <<<EMAIL>>>)

4.3.0 / 2020-03-24
==================

**features**
  * [[`705673d`](http://github.com/koajs/bodyparser/commit/705673d634818727dbdb25ee999560970bd268a2)] - feat: support xml (#131) (TZ | 天猪 <<<EMAIL>>>)

**others**
  * [[`6fd7e9c`](http://github.com/koajs/bodyparser/commit/6fd7e9c321684adc239d2afb270782c21d0b6231)] - docs: add multipart tips (dead-horse <<<EMAIL>>>)
  * [[`57c0022`](http://github.com/koajs/bodyparser/commit/57c00225d54b5b5dd1a7526478ad3eae8495222f)] - Fix typo in README.md (#112) (Adrian Pascu <<<EMAIL>>>)

4.2.1 / 2018-05-21
==================

**others**
  * [[`b270d5d`](http://github.com/koajs/bodyparser/commit/b270d5d138662f41dc63527505ea02dea0c1e7e8)] - deps: upgrade co-body (#104) (Haoliang Gao <<<EMAIL>>>)
  * [[`d234345`](http://github.com/koajs/bodyparser/commit/d234345ffa2dadbab2ef0ce970fb8a58059e5f47)] - docs(readme): update opts encode -> encoding (#103) (Matthew Scragg <<<EMAIL>>>)
  * [[`db193f5`](http://github.com/koajs/bodyparser/commit/db193f5d46860393521ad38f90a554968b2ba98a)] - chore:replace indexOf with includes (#90) (coderzzp <<<EMAIL>>>)

4.2.0 / 2017-03-21
==================

  * feat: ctx.request.rawBody to get raw request body (#70)

4.1.0 / 2017-03-02
==================

  * deps: upgrade co-body@5 (#64)

4.0.0 / 2017-02-27
==================

  * refactor: use async function and support koa@2 (#62)

2.3.0 / 2016-11-14
==================

  * feat: support dynamic disable body parser

2.2.0 / 2016-05-16
==================

  * feat: support enableTypes and text (#44)

2.1.0 / 2016-05-10
==================

  * deps: co-body@4

2.0.1 / 2015-08-12
==================

  * chore: upgrade co-body@3.1.0

2.0.0 / 2015-05-07
==================

  * deps: co-body@2, default to strict mode

1.6.0 / 2015-05-01
==================

  * feat: support custom error handler

1.5.0 / 2015-04-04
==================

  * Use an empty object instead of null, if no body is parsed

1.4.1 / 2015-03-10
==================

  * bump co-body@1.1.0

1.4.0 / 2015-02-26
==================

  * feat: custom json request detect

1.3.1 / 2015-01-27
==================

  * fix: extend

1.3.0 / 2014-11-27
==================

  * support extendTypes
  * Merge pull request #8 from coderhaoxin/json-patch
  * add support for json patch

1.2.0 / 2014-11-07
==================

  * add example.js
  * bump dependencies
  * Merge pull request #7 from rudijs/develop
  * Add support for JSON-API

1.1.0 / 2014-10-28
==================

  * Merge pull request #6 from tunnckoCore/master
  * resolve https://github.com/tunnckoCore/koa-better-body/issues/3#issuecomment-60458238

1.0.0 / 2014-04-23
==================

  * update readme
  * refactor

0.1.0 / 2014-03-06
==================

  * Merge pull request #2 from fengmk2/remove-co
  * Remove co deps and improve coverage to 100%

0.0.2 / 2014-02-26
==================

  * Merge pull request #1 from fengmk2/jsonLimit
  * add jsonLimit options to fix json and form body limit confuse

0.0.1 / 2014-02-18
==================

  * update package name, merge middleware into module.exports
  * complete readme
  * complete bodyparser and bodyparser.middleware
  * Initial commit
