
6.1.0 / 2020-08-12
==================

**features**
  * [[`960986e`](http://github.com/cojs/co-body/commit/960986ef7518ae7ed05f4a2deab44395ffeaa796)] - feat: throw one type error for json parse strict = true (#74) (<PERSON><PERSON><PERSON><PERSON> <<<EMAIL>>>)

**others**
  * [[`a847bfd`](http://github.com/cojs/co-body/commit/a847bfd6e9648138669791c5106439aa080c03ec)] - update: lib/json.js use const define len (#72) (YuLe <<<EMAIL>>>)
  * [[`db6041c`](http://github.com/cojs/co-body/commit/db6041c27ce9a6b280aa49c88d82e3ee0da6a844)] - chore: add codecov (#66) (Haoliang <PERSON> <<<EMAIL>>>)

6.0.0 / 2018-05-21
==================

**features**
  * [[`ad6b34d`](http://github.com/cojs/co-body/commit/ad6b34d72886001215a7ed71861b63dbddbbf40b)] - feat: use async function (#65) (Haoliang Gao <<<EMAIL>>>)

5.2.0 / 2018-05-02
==================

**features**
  * [[`f65a2d8`](http://github.com/cojs/co-body/commit/f65a2d8f7ebf4426138035af6d7e7f02272441f2)] - feat: impl text parser support encoding: false (#64) (killa <<<EMAIL>>>)

5.1.1 / 2017-03-24
==================

  * fix: getOptions change to clone
  * fix: ensure options are independent in each request

5.1.0 / 2017-03-21
==================

  * feat: add options to support return raw body (#56)

5.0.3 / 2017-03-19
==================

  * fix: ensure inflate in promise chain (#54)

5.0.2 / 2017-03-10
==================

  * fix: keep compatibility with qs@4 (#53)

5.0.1 / 2017-03-06
==================

  * dpes: qs@6.4.0

5.0.0 / 2017-03-02
==================

  * deps: upgrade qs to 6.x (#52)

4.2.0 / 2016-05-05
==================

  * test: test on node 4, 5, 6
  * feat: Added support for request body inflation

4.1.0 / 2016-05-05
==================

  * feat: form parse support custom qs module

4.0.0 / 2015-08-15
==================

  * Switch to Promises instead of thunks

3.1.0 / 2015-08-06
==================

 * travis: add v2, v3, remove 0.11
 * add custom types options
 * use type-is

3.0.0 / 2015-07-25
==================

 * Updated dependencies. Added qs options support via queryString option key. (@yanickrochon)
   * upgrade qs@4.0.0, raw-body@2.1.2

2.0.0 / 2015-05-04
==================

  * json parser support strict mode

1.2.0 / 2015-04-29
==================

 * Add JSON-LD as known JSON-Type (@vanthome)

1.1.0 / 2015-02-27
==================

 * Fix content-length zero should not parse json
 * Bump deps, qs@~2.3.3, raw-body@~1.3.3
 * add support for `text/plain`
 * json support for `application/json-patch+json`, `application/vnd.api+json` and `application/csp-report`
