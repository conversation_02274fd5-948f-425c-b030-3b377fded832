{"_args": [["http-assert@1.5.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "http-assert@1.5.0", "_id": "http-assert@1.5.0", "_inBundle": false, "_integrity": "sha512-uPpH7OKX4H25hBmU6G1jWNaqJGpTXxey+YOUizJUAgu0AjLUeC8D73hTrhvDS5D+GJN1DN1+hhc/eF/wpxtp0w==", "_location": "/http-assert", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "http-assert@1.5.0", "name": "http-assert", "escapedName": "http-assert", "rawSpec": "1.5.0", "saveSpec": null, "fetchSpec": "1.5.0"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/http-assert/-/http-assert-1.5.0.tgz", "_spec": "1.5.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/jshttp/http-assert/issues"}, "dependencies": {"deep-equal": "~1.0.1", "http-errors": "~1.8.0"}, "description": "assert with status codes", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.24.2", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "istanbul": "0.4.5", "mocha": "9.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/jshttp/http-assert#readme", "keywords": ["assert", "http"], "license": "MIT", "name": "http-assert", "repository": {"type": "git", "url": "git+https://github.com/jshttp/http-assert.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.5.0"}