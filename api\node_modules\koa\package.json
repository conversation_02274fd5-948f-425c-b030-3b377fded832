{"_args": [["koa@2.14.2", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "koa@2.14.2", "_id": "koa@2.14.2", "_inBundle": false, "_integrity": "sha512-VFI2bpJaodz6P7x2uyLiX6RLYpZmOJqNmoCst/Yyd7hQlszyPwG/I9CQJ63nOtKSxpt5M7NH67V6nJL2BwCl7g==", "_location": "/koa", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "koa@2.14.2", "name": "koa", "escapedName": "koa", "rawSpec": "2.14.2", "saveSpec": null, "fetchSpec": "2.14.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/koa/-/koa-2.14.2.tgz", "_spec": "2.14.2", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/koajs/koa/issues"}, "dependencies": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.8.0", "debug": "^4.3.2", "delegates": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.0.4", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^2.0.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}, "description": "Koa web app framework", "devDependencies": {"eslint": "^7.32.0", "eslint-config-koa": "^2.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0", "gen-esm-wrapper": "^1.0.6", "jest": "^27.0.6", "supertest": "^3.1.0"}, "engines": {"node": "^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4"}, "exports": {".": {"require": "./lib/application.js", "import": "./dist/koa.mjs"}, "./lib/request": "./lib/request.js", "./lib/request.js": "./lib/request.js", "./lib/response": "./lib/response.js", "./lib/response.js": "./lib/response.js", "./lib/application": "./lib/application.js", "./lib/application.js": "./lib/application.js", "./lib/context": "./lib/context.js", "./lib/context.js": "./lib/context.js", "./*": "./*.js", "./*.js": "./*.js", "./package": "./package.json", "./package.json": "./package.json"}, "files": ["dist", "lib"], "homepage": "https://github.com/koajs/koa#readme", "jest": {"testEnvironment": "node"}, "keywords": ["web", "app", "http", "application", "framework", "middleware", "rack"], "license": "MIT", "main": "lib/application.js", "name": "koa", "repository": {"type": "git", "url": "git+https://github.com/koajs/koa.git"}, "scripts": {"authors": "git log --format='%aN <%aE>' | sort -u > AUTHORS", "build": "gen-esm-wrapper . ./dist/koa.mjs", "lint": "eslint --ignore-path .gitignore .", "prepare": "npm run build", "test": "jest --force<PERSON>xit"}, "version": "2.14.2"}