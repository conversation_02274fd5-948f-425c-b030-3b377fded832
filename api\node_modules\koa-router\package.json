{"_args": [["koa-router@12.0.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "koa-router@12.0.0", "_id": "koa-router@12.0.0", "_inBundle": false, "_integrity": "sha512-zGrdiXygGYW8WvrzeGsHZvKnHs4DzyGoqJ9a8iHlRkiwuEAOAPyI27//OlhoWdgFAEIM3qbUgr0KCuRaP/TCag==", "_location": "/koa-router", "_phantomChildren": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "toidentifier": "1.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "koa-router@12.0.0", "name": "koa-router", "escapedName": "koa-router", "rawSpec": "12.0.0", "saveSpec": null, "fetchSpec": "12.0.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/koa-router/-/koa-router-12.0.0.tgz", "_spec": "12.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/koajs/router/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "@koajs"}], "dependencies": {"http-errors": "^2.0.0", "koa-compose": "^4.1.0", "methods": "^1.1.2", "path-to-regexp": "^6.2.1"}, "description": "Router middleware for koa. Maintained by Forward Email and Lad.", "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@ladjs/env": "^3.0.0", "ava": "^4.3.0", "cross-env": "^7.0.3", "eslint": "^8.19.0", "eslint-config-xo-lass": "^2.0.1", "expect.js": "^0.3.1", "fixpack": "^4.0.0", "husky": "^8.0.1", "jsdoc-to-markdown": "^7.1.1", "koa": "^2.13.4", "lint-staged": "^13.0.3", "mocha": "^10.0.0", "nyc": "^15.1.0", "remark-cli": "^11.0.0", "remark-preset-github": "^4.0.4", "should": "^13.2.3", "supertest": "^6.2.4", "wrk": "^1.2.1", "xo": "^0.50.0"}, "engines": {"node": ">= 12"}, "files": ["lib"], "homepage": "https://github.com/koajs/router", "keywords": ["koa", "middleware", "route", "router"], "license": "MIT", "main": "lib/router.js", "name": "koa-router", "repository": {"type": "git", "url": "git+https://github.com/koajs/router.git"}, "scripts": {"bench": "make -C bench", "coverage": "nyc npm run test", "docs": "NODE_ENV=test jsdoc2md -t ./lib/API_tpl.hbs --src ./lib/*.js  >| API.md", "lint": "xo --fix && remark . -qfo && fixpack", "prepare": "husky install", "pretest": "npm run lint", "test": "mocha test/**/*.js", "test:watch": "mocha test/**/*.js --watch"}, "version": "12.0.0"}