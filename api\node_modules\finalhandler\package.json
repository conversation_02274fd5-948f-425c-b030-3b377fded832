{"_from": "finalhandler@1.2.0", "_id": "finalhandler@1.2.0", "_inBundle": false, "_integrity": "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==", "_location": "/finalhandler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "finalhandler@1.2.0", "name": "finalhandler", "escapedName": "finalhandler", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/finalhandler/-/finalhandler-1.2.0.tgz", "_shasum": "7d23fe5731b207b4640e4fcd00aec1f9207a7b32", "_spec": "finalhandler@1.2.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "bundleDependencies": false, "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "deprecated": false, "description": "Node.js final http responder", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0", "readable-stream": "2.3.6", "safe-buffer": "5.2.1", "supertest": "6.2.2"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "SECURITY.md", "index.js"], "homepage": "https://github.com/pillarjs/finalhandler#readme", "license": "MIT", "name": "finalhandler", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/finalhandler.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.2.0"}