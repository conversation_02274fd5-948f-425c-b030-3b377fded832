{"_args": [["keygrip@1.1.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "keygrip@1.1.0", "_id": "keygrip@1.1.0", "_inBundle": false, "_integrity": "sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==", "_location": "/keygrip", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "keygrip@1.1.0", "name": "keygrip", "escapedName": "keygrip", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/cookies"], "_resolved": "https://registry.npmmirror.com/keygrip/-/keygrip-1.1.0.tgz", "_spec": "1.1.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/crypto-utils/keygrip/issues"}, "dependencies": {"tsscmp": "1.0.6"}, "description": "Key signing and verification for rotated credentials", "devDependencies": {"mocha": "6.1.4", "nyc": "14.0.0"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/crypto-utils/keygrip#readme", "license": "MIT", "name": "keygrip", "repository": {"type": "git", "url": "git+https://github.com/crypto-utils/keygrip.git"}, "scripts": {"test": "mocha --reporter spec test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "version": "1.1.0"}