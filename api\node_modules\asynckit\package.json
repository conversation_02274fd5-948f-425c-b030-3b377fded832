{"_from": "asynckit@^0.4.0", "_id": "asynckit@0.4.0", "_inBundle": false, "_integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "_location": "/asynckit", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "asynckit@^0.4.0", "name": "asynckit", "escapedName": "asynckit", "rawSpec": "^0.4.0", "saveSpec": null, "fetchSpec": "^0.4.0"}, "_requiredBy": ["/form-data"], "_resolved": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "_shasum": "c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79", "_spec": "asynckit@^0.4.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口\\node_modules\\form-data", "author": {"name": "Alex Indigo", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/alexindigo/asynckit/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Minimal async jobs utility library, with streams support", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "homepage": "https://github.com/alexindigo/asynckit#readme", "keywords": ["async", "jobs", "parallel", "serial", "iterator", "array", "object", "stream", "destroy", "terminate", "abort"], "license": "MIT", "main": "index.js", "name": "asynckit", "pre-commit": ["clean", "lint", "test", "browser", "report", "size"], "repository": {"type": "git", "url": "git+https://github.com/alexindigo/asynckit.git"}, "scripts": {"browser": "browserify -t browserify-istanbul test/lib/browserify_adjustment.js test/test-*.js | obake --coverage | tap-spec", "clean": "rimraf coverage", "debug": "tape test/test-*.js", "lint": "eslint *.js lib/*.js test/*.js", "report": "istanbul report", "size": "browserify index.js | size-table asynckit", "test": "istanbul cover --reporter=json tape -- 'test/test-*.js' | tap-spec", "win-test": "tape test/test-*.js"}, "version": "0.4.0"}