{"_args": [["koa-convert@2.0.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "koa-convert@2.0.0", "_id": "koa-convert@2.0.0", "_inBundle": false, "_integrity": "sha512-asOvN6bFlSnxewce2e/DK3p4tltyfC4VM7ZwuTuepI7dEQVcvpyFuBcEARu1+Hxg8DIwytce2n7jrZtRlPrARA==", "_location": "/koa-convert", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "koa-convert@2.0.0", "name": "koa-convert", "escapedName": "koa-convert", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/koa-convert/-/koa-convert-2.0.0.tgz", "_spec": "2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "gyson", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/gyson/koa-convert/issues"}, "contributors": [{"name": "gyson", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "imed-jabe<PERSON>@outlook.com", "url": "https://www.3imed-jaberi.com"}], "dependencies": {"co": "^4.6.0", "koa-compose": "^4.1.0"}, "description": "convert modern Koa legacy generator-based middleware to promise-based middleware", "devDependencies": {"koa": "^2.13.0", "koa-v1": "npm:koa@1.7.0", "mocha": "^7.1.1", "nyc": "^15.1.0", "rimraf": "^3.0.2", "standard": "^14.3.4", "supertest": "^4.0.2"}, "engines": {"node": ">= 10"}, "files": ["index.js"], "homepage": "https://github.com/gyson/koa-convert#readme", "keywords": ["koa", "middleware", "convert", "back", "generator", "promise", "generator-based-middleware", "promise-based-middleware", "support"], "license": "MIT", "main": "index.js", "name": "koa-convert", "repository": {"type": "git", "url": "git+https://github.com/gyson/koa-convert.git"}, "scripts": {"ci": "npm run coverage", "coverage": "nyc npm run test", "lint": "standard", "precoverage": "rimraf .nyc_output coverage", "pretest": "npm run lint", "test": "mocha index.spec.js --exit"}, "standard": {"ignore": ["index.spec.js"]}, "version": "2.0.0"}