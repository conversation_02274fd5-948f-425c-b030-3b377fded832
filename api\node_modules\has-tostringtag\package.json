{"_args": [["has-tostringtag@1.0.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "has-tostringtag@1.0.0", "_id": "has-tostringtag@1.0.0", "_inBundle": false, "_integrity": "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==", "_location": "/has-tostringtag", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has-tostringtag@1.0.0", "name": "has-tostringtag", "escapedName": "has-tostringtag", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/is-generator-function"], "_resolved": "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/has-tostringtag/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"has-symbols": "^1.0.2"}, "description": "Determine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.", "devDependencies": {"@ljharb/eslint-config": "^17.6.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "core-js": "^2.6.12", "eslint": "^7.32.0", "get-own-property-symbols": "^0.9.5", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.3.0"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./shams": "./shams.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/has-tostringtag#readme", "keywords": ["javascript", "ecmascript", "symbol", "symbols", "tostringtag", "Symbol.toStringTag"], "license": "MIT", "main": "index.js", "name": "has-tostringtag", "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-tostringtag.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "test:staging": "nyc node --harmony --es-staging test", "test:stock": "nyc node test", "tests-only": "npm run test:stock && npm run test:staging && npm run test:shams", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.0.0"}