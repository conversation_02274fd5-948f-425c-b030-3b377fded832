{"_args": [["call-bind@1.0.2", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "call-bind@1.0.2", "_id": "call-bind@1.0.2", "_inBundle": false, "_integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "_location": "/call-bind", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "call-bind@1.0.2", "name": "call-bind", "escapedName": "call-bind", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/side-channel"], "_resolved": "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz", "_spec": "1.0.2", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/call-bind/issues"}, "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "description": "Robustly `.call.bind()` a function", "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.17.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.1.1"}, "exports": {".": [{"default": "./index.js"}, "./index.js"], "./callBound": [{"default": "./callBound.js"}, "./callBound.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/call-bind#readme", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "license": "MIT", "main": "index.js", "name": "call-bind", "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bind.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/*'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.0.2"}