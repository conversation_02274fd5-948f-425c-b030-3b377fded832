{"_args": [["is-generator-function@1.0.10", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "is-generator-function@1.0.10", "_id": "is-generator-function@1.0.10", "_inBundle": false, "_integrity": "sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==", "_location": "/is-generator-function", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-generator-function@1.0.10", "name": "is-generator-function", "escapedName": "is-generator-function", "rawSpec": "1.0.10", "saveSpec": null, "fetchSpec": "1.0.10"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/is-generator-function/-/is-generator-function-1.0.10.tgz", "_spec": "1.0.10", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-generator-function/issues"}, "dependencies": {"has-tostringtag": "^1.0.0"}, "description": "Determine if a function is a native generator function.", "devDependencies": {"@ljharb/eslint-config": "^17.6.0", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "core-js": "^2.6.5 || ^3.16.0", "eslint": "^7.32.0", "make-generator-function": "^2.0.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.3.0", "uglify-register": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-generator-function#readme", "keywords": ["generator", "generator function", "es6", "es2015", "yield", "function", "function*"], "license": "MIT", "main": "index.js", "name": "is-generator-function", "repository": {"type": "git", "url": "git://github.com/inspect-js/is-generator-function.git"}, "scripts": {"lint": "eslint .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "test:all": "npm run test:index && npm run test:corejs && npm run test:uglified", "test:corejs": "node test/corejs", "test:harmony": "node  --es-staging --harmony test && node  --es-staging --harmony test/corejs && node --es-staging --harmony test/uglified", "test:index": "node test", "test:uglified": "node test/uglified", "tests-only": "nyc npm run test:all", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.0.10"}