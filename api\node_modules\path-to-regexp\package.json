{"_args": [["path-to-regexp@6.2.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "path-to-regexp@6.2.1", "_id": "path-to-regexp@6.2.1", "_inBundle": false, "_integrity": "sha512-JLyh7xT1kizaEvcaXOQwOc2/Yhw6KZOvPf1S8401UyLk86CU79LN3vl7ztXGm/pZ+YjoyAJ4rxmHwbkBXJX+yw==", "_location": "/path-to-regexp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "path-to-regexp@6.2.1", "name": "path-to-regexp", "escapedName": "path-to-regexp", "rawSpec": "6.2.1", "saveSpec": null, "fetchSpec": "6.2.1"}, "_requiredBy": ["/koa-router"], "_resolved": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-6.2.1.tgz", "_spec": "6.2.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "description": "Express style path to RegExp utility", "devDependencies": {"@borderless/ts-scripts": "^0.8.0", "@size-limit/preset-small-lib": "^7.0.8", "@types/jest": "^27.4.0", "@types/node": "^17.0.17", "@types/semver": "^7.3.1", "semver": "^7.3.5", "size-limit": "^7.0.8", "typescript": "^4.5.5"}, "files": ["dist.es2015/", "dist/"], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "main": "dist/index.js", "module": "dist.es2015/index.js", "name": "path-to-regexp", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/pillarjs/path-to-regexp.git"}, "scripts": {"build": "ts-scripts build", "format": "ts-scripts format", "lint": "ts-scripts lint", "prepare": "ts-scripts install && npm run build", "size": "size-limit", "specs": "ts-scripts specs", "test": "ts-scripts test && npm run size"}, "sideEffects": false, "size-limit": [{"path": "dist.es2015/index.js", "limit": "2.1 kB"}], "ts-scripts": {"dist": ["dist", "dist.es2015"], "project": ["tsconfig.build.json", "tsconfig.es2015.json"]}, "typings": "dist/index.d.ts", "version": "6.2.1"}