{"_from": "statuses@2.0.1", "_id": "statuses@2.0.1", "_inBundle": false, "_integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "_location": "/finalhandler/statuses", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "statuses@2.0.1", "name": "statuses", "escapedName": "statuses", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/finalhandler"], "_resolved": "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz", "_shasum": "55cb000ccf1d48728bd23c685a063998cf1a1b63", "_spec": "statuses@2.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口\\node_modules\\finalhandler", "bugs": {"url": "https://github.com/jshttp/statuses/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "deprecated": false, "description": "HTTP status utility", "devDependencies": {"csv-parse": "4.14.2", "eslint": "7.17.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.2.1", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "index.js", "codes.json", "LICENSE"], "homepage": "https://github.com/jshttp/statuses#readme", "keywords": ["http", "status", "code"], "license": "MIT", "name": "statuses", "repository": {"type": "git", "url": "git+https://github.com/jshttp/statuses.git"}, "scripts": {"build": "node scripts/build.js", "fetch": "node scripts/fetch-apache.js && node scripts/fetch-iana.js && node scripts/fetch-nginx.js && node scripts/fetch-node.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "2.0.1"}