{"_args": [["http-errors@1.8.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "http-errors@1.8.1", "_id": "http-errors@1.8.1", "_inBundle": false, "_integrity": "sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==", "_location": "/http-errors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "http-errors@1.8.1", "name": "http-errors", "escapedName": "http-errors", "rawSpec": "1.8.1", "saveSpec": null, "fetchSpec": "1.8.1"}, "_requiredBy": ["/http-assert", "/koa", "/koa-send"], "_resolved": "https://registry.npmmirror.com/http-errors/-/http-errors-1.8.1.tgz", "_spec": "1.8.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.1"}, "description": "Create HTTP error objects", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.3", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "homepage": "https://github.com/jshttp/http-errors#readme", "keywords": ["http", "error"], "license": "MIT", "name": "http-errors", "repository": {"type": "git", "url": "git+https://github.com/jshttp/http-errors.git"}, "scripts": {"lint": "eslint . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.8.1"}