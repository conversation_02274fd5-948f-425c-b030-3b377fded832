{"_args": [["fs@0.0.1-security", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "fs@0.0.1-security", "_id": "fs@0.0.1-security", "_inBundle": false, "_integrity": "sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==", "_location": "/fs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "fs@0.0.1-security", "name": "fs", "escapedName": "fs", "rawSpec": "0.0.1-security", "saveSpec": null, "fetchSpec": "0.0.1-security"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/fs/-/fs-0.0.1-security.tgz", "_spec": "0.0.1-security", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": "", "bugs": {"url": "https://github.com/npm/security-holder/issues"}, "description": "This package name is not currently in use, but was formerly occupied by another package. To avoid malicious use, npm is hanging on to the package name, but loosely, and we'll probably give it to you if you want it.", "homepage": "https://github.com/npm/security-holder#readme", "keywords": [], "license": "ISC", "main": "index.js", "name": "fs", "repository": {"type": "git", "url": "git+https://github.com/npm/security-holder.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "0.0.1-security"}