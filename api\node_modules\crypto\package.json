{"_from": "crypto@^1.0.1", "_id": "crypto@1.0.1", "_inBundle": false, "_integrity": "sha512-VxBKmeNcqQdiUQUW2Tzq0t377b54N2bMtXO/qiLa+6eRRmmC4qT3D4OnTGoT/U6O9aklQ/jTwbOtRMTTY8G0Ig==", "_location": "/crypto", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "crypto@^1.0.1", "name": "crypto", "escapedName": "crypto", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/crypto/-/crypto-1.0.1.tgz", "_shasum": "2af1b7cad8175d24c8a1b0778255794a21803037", "_spec": "crypto@^1.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": "", "bugs": {"url": "https://github.com/npm/deprecate-holder/issues"}, "bundleDependencies": false, "deprecated": "This package is no longer supported. It's now a built-in Node module. If you've depended on crypto, you should switch to the one that's built-in.", "description": "This package is no longer supported and has been deprecated. To avoid malicious use, npm is hanging on to the package name.", "homepage": "https://github.com/npm/deprecate-holder#readme", "license": "ISC", "main": "index.js", "name": "crypto", "repository": {"type": "git", "url": "git+https://github.com/npm/deprecate-holder.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.1"}