{"_args": [["only@0.0.2", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "only@0.0.2", "_id": "only@0.0.2", "_inBundle": false, "_integrity": "sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ==", "_location": "/only", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "only@0.0.2", "name": "only", "escapedName": "only", "rawSpec": "0.0.2", "saveSpec": null, "fetchSpec": "0.0.2"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/only/-/only-0.0.2.tgz", "_spec": "0.0.2", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/visionmedia/node-only/issues"}, "dependencies": {}, "description": "return whitelisted properties of an object", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/node-only#readme", "keywords": ["utility", "util", "object", "whitelist"], "main": "index", "name": "only", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-only.git"}, "version": "0.0.2"}