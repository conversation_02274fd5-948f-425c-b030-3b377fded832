{"_args": [["koa-compose@4.1.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "koa-compose@4.1.0", "_id": "koa-compose@4.1.0", "_inBundle": false, "_integrity": "sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==", "_location": "/koa-compose", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "koa-compose@4.1.0", "name": "koa-compose", "escapedName": "koa-compose", "rawSpec": "4.1.0", "saveSpec": null, "fetchSpec": "4.1.0"}, "_requiredBy": ["/koa", "/koa-convert", "/koa-router"], "_resolved": "https://registry.npmmirror.com/koa-compose/-/koa-compose-4.1.0.tgz", "_spec": "4.1.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/koajs/compose/issues"}, "dependencies": {}, "description": "compose Koa middleware", "devDependencies": {"codecov": "^3.0.0", "jest": "^21.0.0", "matcha": "^0.7.0", "standard": "^10.0.3"}, "files": ["index.js"], "homepage": "https://github.com/koajs/compose#readme", "jest": {"testEnvironment": "node"}, "keywords": ["koa", "middleware", "compose"], "license": "MIT", "name": "koa-compose", "repository": {"type": "git", "url": "git+https://github.com/koajs/compose.git"}, "scripts": {"bench": "matcha bench/bench.js", "lint": "standard --fix .", "test": "jest --forceExit --coverage"}, "version": "4.1.0"}