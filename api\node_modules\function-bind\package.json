{"_args": [["function-bind@1.1.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "function-bind@1.1.1", "_id": "function-bind@1.1.1", "_inBundle": false, "_integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==", "_location": "/function-bind", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "function-bind@1.1.1", "name": "function-bind", "escapedName": "function-bind", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/call-bind", "/get-intrinsic", "/has"], "_resolved": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz", "_spec": "1.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/function-bind/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "dependencies": {}, "description": "Implementation of Function.prototype.bind", "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.5.0", "jscs": "^3.0.7", "tape": "^4.8.0"}, "homepage": "https://github.com/Raynos/function-bind", "keywords": ["function", "bind", "shim", "es5"], "license": "MIT", "main": "index", "name": "function-bind", "repository": {"type": "git", "url": "git://github.com/Raynos/function-bind.git"}, "scripts": {"coverage": "covert test/*.js", "eslint": "eslint *.js */*.js", "jscs": "jscs *.js */*.js", "lint": "npm run jscs && npm run eslint", "posttest": "npm run coverage -- --quiet", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node test"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.1.1"}