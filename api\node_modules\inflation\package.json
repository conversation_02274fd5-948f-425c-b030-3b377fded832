{"_args": [["inflation@2.0.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "inflation@2.0.0", "_id": "inflation@2.0.0", "_inBundle": false, "_integrity": "sha512-m3xv4hJYR2oXw4o4Y5l6P5P16WYmazYof+el6Al3f+YlggGj6qT9kImBAnzDelRALnP5d3h4jGBPKzYCizjZZw==", "_location": "/inflation", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "inflation@2.0.0", "name": "inflation", "escapedName": "inflation", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/co-body"], "_resolved": "https://registry.npmmirror.com/inflation/-/inflation-2.0.0.tgz", "_spec": "2.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/stream-utils/inflation/issues"}, "description": "Easily unzip an HTTP stream", "devDependencies": {"istanbul": "0.2.10", "mocha": "~1.20.1", "readable-stream": "~1.0.27", "should": "4.0.4"}, "engines": {"node": ">= 0.8.0"}, "files": ["index.js"], "homepage": "https://github.com/stream-utils/inflation#readme", "keywords": ["decompress", "unzip", "inflate", "zlib", "gunzip"], "license": "MIT", "name": "inflation", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/inflation.git"}, "scripts": {"test": "mocha --reporter spec --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "version": "2.0.0"}