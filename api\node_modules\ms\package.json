{"_args": [["ms@2.1.2", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "ms@2.1.2", "_id": "ms@2.1.2", "_inBundle": false, "_integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "_location": "/ms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ms@2.1.2", "name": "ms", "escapedName": "ms", "rawSpec": "2.1.2", "saveSpec": null, "fetchSpec": "2.1.2"}, "_requiredBy": ["/debug", "/koa-static/debug"], "_resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "_spec": "2.1.2", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "description": "Tiny millisecond conversion utility", "devDependencies": {"eslint": "4.12.1", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "files": ["index.js"], "homepage": "https://github.com/zeit/ms#readme", "license": "MIT", "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "main": "./index", "name": "ms", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "scripts": {"lint": "eslint lib/* bin/*", "precommit": "lint-staged", "test": "mocha tests.js"}, "version": "2.1.2"}