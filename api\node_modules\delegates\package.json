{"_args": [["delegates@1.0.0", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "delegates@1.0.0", "_id": "delegates@1.0.0", "_inBundle": false, "_integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==", "_location": "/delegates", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "delegates@1.0.0", "name": "delegates", "escapedName": "delegates", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/delegates/-/delegates-1.0.0.tgz", "_spec": "1.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "dependencies": {}, "description": "delegate methods and accessors to another property", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/node-delegates#readme", "keywords": ["delegate", "delegation"], "license": "MIT", "name": "delegates", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-delegates.git"}, "version": "1.0.0"}