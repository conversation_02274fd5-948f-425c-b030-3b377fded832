{"_args": [["debug@4.3.4", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "debug@4.3.4", "_id": "debug@4.3.4", "_inBundle": false, "_integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "_location": "/debug", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "debug@4.3.4", "name": "debug", "escapedName": "debug", "rawSpec": "4.3.4", "saveSpec": null, "fetchSpec": "4.3.4"}, "_requiredBy": ["/koa", "/koa-send"], "_resolved": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "_spec": "4.3.4", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "./src/browser.js", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"ms": "2.1.2"}, "description": "Lightweight debugging utility for Node.js and the browser", "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "xo": "^0.23.0"}, "engines": {"node": ">=6.0"}, "files": ["src", "LICENSE", "README.md"], "homepage": "https://github.com/debug-js/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./src/index.js", "name": "debug", "peerDependenciesMeta": {"supports-color": {"optional": true}}, "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls", "test:node": "istanbul cover _mocha -- test.js"}, "version": "4.3.4"}