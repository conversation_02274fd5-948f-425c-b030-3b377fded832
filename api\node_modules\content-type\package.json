{"_args": [["content-type@1.0.5", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "content-type@1.0.5", "_id": "content-type@1.0.5", "_inBundle": false, "_integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "_location": "/content-type", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "content-type@1.0.5", "name": "content-type", "escapedName": "content-type", "rawSpec": "1.0.5", "saveSpec": null, "fetchSpec": "1.0.5"}, "_requiredBy": ["/koa"], "_resolved": "https://registry.npmmirror.com/content-type/-/content-type-1.0.5.tgz", "_spec": "1.0.5", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/content-type/issues"}, "description": "Create and parse HTTP Content-Type header", "devDependencies": {"deep-equal": "1.0.1", "eslint": "8.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "10.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-type#readme", "keywords": ["content-type", "http", "req", "res", "rfc7231"], "license": "MIT", "name": "content-type", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-type.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.0.5"}