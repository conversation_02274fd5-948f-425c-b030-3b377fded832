{"_args": [["has-proto@1.0.1", "C:\\Users\\<USER>\\Desktop\\autman\\测试接口"]], "_from": "has-proto@1.0.1", "_id": "has-proto@1.0.1", "_inBundle": false, "_integrity": "sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==", "_location": "/has-proto", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has-proto@1.0.1", "name": "has-proto", "escapedName": "has-proto", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/get-intrinsic"], "_resolved": "https://registry.npmmirror.com/has-proto/-/has-proto-1.0.1.tgz", "_spec": "1.0.1", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/has-proto/issues"}, "description": "Does this environment have the ability to get the [[Prototype]] of an object on creation with `__proto__`?", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/has-proto#readme", "keywords": ["prototype", "proto", "set", "get", "__proto__", "getPrototypeOf", "setPrototypeOf", "has"], "license": "MIT", "main": "index.js", "name": "has-proto", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-proto.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.0.1"}