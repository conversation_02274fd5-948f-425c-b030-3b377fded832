{"_from": "qs@6.11.0", "_id": "qs@6.11.0", "_inBundle": false, "_integrity": "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==", "_location": "/body-parser/qs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "qs@6.11.0", "name": "qs", "escapedName": "qs", "rawSpec": "6.11.0", "saveSpec": null, "fetchSpec": "6.11.0"}, "_requiredBy": ["/body-parser"], "_resolved": "https://registry.npmmirror.com/qs/-/qs-6.11.0.tgz", "_shasum": "fd0d963446f7a65e1367e01abd85429453f0c37a", "_spec": "qs@6.11.0", "_where": "C:\\Users\\<USER>\\Desktop\\autman\\测试接口\\node_modules\\body-parser", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"side-channel": "^1.0.4"}, "deprecated": false, "description": "A querystring parser that supports nesting and arrays, with a depth limit", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "browserify": "^16.5.2", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has-symbols": "^1.0.3", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "mkdirp": "^0.5.5", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.2", "qs-iconv": "^1.0.4", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "tape": "^5.5.3"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/qs", "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "qs", "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/qs.git"}, "scripts": {"dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint --ext=js,mjs .", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "readme": "evalmd README.md", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'"}, "version": "6.11.0"}